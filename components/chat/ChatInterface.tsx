'use client';

import { useEffect, useRef, useState } from 'react';
import { ChevronDown, Loader2 } from 'lucide-react';
import { Chatroom } from '@/types';
import { useChatStore } from '@/stores/chatStore';
import { useUIStore } from '@/stores/uiStore';
import { ChatMessage } from './ChatMessage';
import { TypingIndicator } from './TypingIndicator';
import { ChatInput } from './ChatInput';
import { MessageFormData } from '@/lib/validations';
import { scrollToBottom, isAtBottom, cn } from '@/lib/utils';

interface ChatInterfaceProps {
  chatroom: Chatroom;
}

export function ChatInterface({ chatroom }: ChatInterfaceProps) {
  const {
    messages,
    isLoading,
    isTyping,
    hasMoreMessages,
    sendMessage,
    loadMoreMessages,
  } = useChatStore();

  const { showToast } = useUIStore();
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      const shouldAutoScroll = isAtBottom(container, 100);
      
      if (shouldAutoScroll) {
        setTimeout(() => {
          scrollToBottom(container);
        }, 100);
      }
    }
  }, [messages, isTyping]);

  // Handle scroll to show/hide scroll-to-bottom button
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const atBottom = isAtBottom(container, 100);
      setShowScrollButton(!atBottom && messages.length > 0);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [messages.length]);

  // Load more messages when scrolling to top
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = async () => {
      if (container.scrollTop === 0 && hasMoreMessages && !isLoadingMore) {
        setIsLoadingMore(true);
        const previousScrollHeight = container.scrollHeight;
        
        try {
          await loadMoreMessages();
          
          // Maintain scroll position after loading more messages
          setTimeout(() => {
            const newScrollHeight = container.scrollHeight;
            container.scrollTop = newScrollHeight - previousScrollHeight;
          }, 100);
        } catch (error) {
          showToast({
            type: 'error',
            message: 'Failed to load more messages',
          });
        } finally {
          setIsLoadingMore(false);
        }
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasMoreMessages, isLoadingMore, loadMoreMessages, showToast]);

  const handleSendMessage = async (data: MessageFormData) => {
    try {
      await sendMessage(data);
    } catch (error) {
      throw error; // Let ChatInput handle the error display
    }
  };

  const handleScrollToBottom = () => {
    if (messagesContainerRef.current) {
      scrollToBottom(messagesContainerRef.current);
    }
  };

  // Filter messages for current chatroom
  const chatroomMessages = messages.filter(msg => msg.chatroomId === chatroom.id);

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto scroll-smooth"
      >
        {/* Load More Indicator */}
        {isLoadingMore && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="w-5 h-5 animate-spin text-gray-400 mr-2" />
            <span className="text-sm text-gray-500">Loading more messages...</span>
          </div>
        )}

        {/* No More Messages Indicator */}
        {!hasMoreMessages && chatroomMessages.length > 0 && (
          <div className="text-center py-4">
            <span className="text-xs text-gray-400 bg-gray-100 px-3 py-1 rounded-full">
              Beginning of conversation
            </span>
          </div>
        )}

        {/* Messages */}
        {chatroomMessages.length > 0 ? (
          <div className="space-y-0">
            {chatroomMessages.map((message, index) => (
              <ChatMessage
                key={message.id}
                message={message}
                isLast={index === chatroomMessages.length - 1}
              />
            ))}
          </div>
        ) : !isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-md mx-auto p-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💬</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Start the conversation
              </h3>
              <p className="text-gray-600 mb-4">
                Send a message to begin chatting with Gemini AI. Ask questions, 
                share ideas, or just have a conversation!
              </p>
            </div>
          </div>
        ) : null}

        {/* Typing Indicator */}
        {isTyping && <TypingIndicator />}

        {/* Messages End Marker */}
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to Bottom Button */}
      {showScrollButton && (
        <div className="absolute bottom-20 right-4 z-10">
          <button
            onClick={handleScrollToBottom}
            className="p-2 bg-white border border-gray-300 rounded-full shadow-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="Scroll to bottom"
          >
            <ChevronDown className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      )}

      {/* Chat Input */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isLoading}
      />
    </div>
  );
}
