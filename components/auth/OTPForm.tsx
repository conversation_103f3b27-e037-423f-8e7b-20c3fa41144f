'use client';

import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Shield, Loader2, ArrowLeft, RotateCcw } from 'lucide-react';
import { otpSchema, OTPFormData } from '@/lib/validations';
import { useAuthStore } from '@/stores/authStore';
import { useUIStore } from '@/stores/uiStore';
import { cn } from '@/lib/utils';

interface OTPFormProps {
  phoneNumber: string;
  countryCode: string;
  onBack?: () => void;
  onSuccess?: () => void;
}

export function OTPForm({ phoneNumber, countryCode, onBack, onSuccess }: OTPFormProps) {
  const { verifyOTP, resendOTP, isLoading, error, resetOTPFlow } = useAuthStore();
  const { showToast } = useUIStore();
  const [resendCooldown, setResendCooldown] = useState(0);
  const otpInputRefs = useRef<(HTMLInputElement | null)[]>([]);
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<OTPFormData>({
    resolver: zodResolver(otpSchema),
    mode: 'onChange',
    defaultValues: {
      otp: '',
    },
  });

  const otpValue = watch('otp');

  // Start resend cooldown
  useEffect(() => {
    setResendCooldown(30); // 30 seconds cooldown
    const timer = setInterval(() => {
      setResendCooldown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Focus first input on mount
  useEffect(() => {
    if (otpInputRefs.current[0]) {
      otpInputRefs.current[0].focus();
    }
  }, []);

  const onSubmit = async (data: OTPFormData) => {
    try {
      await verifyOTP(data);

      // Only show success message and call onSuccess if verification actually succeeds
      showToast({
        type: 'success',
        message: 'Phone number verified successfully!',
      });
      onSuccess?.();
    } catch (error) {
      // Show error message for invalid OTP
      showToast({
        type: 'error',
        message: 'Invalid OTP. Please try again.',
      });
      // Clear the OTP input field
      setValue('otp', '');
      // Focus back to first input
      if (otpInputRefs.current[0]) {
        otpInputRefs.current[0].focus();
      }
    }
  };

  const handleResendOTP = async () => {
    if (resendCooldown > 0) return;
    
    try {
      await resendOTP();
      setResendCooldown(30);
      showToast({
        type: 'success',
        message: 'OTP resent successfully!',
      });
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to resend OTP. Please try again.',
      });
    }
  };

  const handleOTPChange = (index: number, value: string) => {
    // Only allow digits
    const digit = value.replace(/\D/g, '').slice(-1);
    
    // Update the current input
    const newOTP = otpValue.split('');
    newOTP[index] = digit;
    const updatedOTP = newOTP.join('');
    
    setValue('otp', updatedOTP, { shouldValidate: true });

    // Move to next input if digit entered
    if (digit && index < 5) {
      otpInputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    // Move to previous input on backspace
    if (e.key === 'Backspace' && !otpValue[index] && index > 0) {
      otpInputRefs.current[index - 1]?.focus();
    }
    
    // Move to next input on arrow right
    if (e.key === 'ArrowRight' && index < 5) {
      otpInputRefs.current[index + 1]?.focus();
    }
    
    // Move to previous input on arrow left
    if (e.key === 'ArrowLeft' && index > 0) {
      otpInputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6);
    setValue('otp', pastedData, { shouldValidate: true });
    
    // Focus the last filled input or the first empty one
    const focusIndex = Math.min(pastedData.length, 5);
    otpInputRefs.current[focusIndex]?.focus();
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <Shield className="w-6 h-6 text-green-600" />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Verify Your Phone
        </h1>
        <p className="text-gray-600 mb-2">
          We've sent a 6-digit code to
        </p>
        <p className="text-gray-900 font-medium">
          {countryCode} {phoneNumber}
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* OTP Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3 text-center">
            Enter verification code
          </label>
          <div className="flex justify-center space-x-2 mb-2">
            {Array.from({ length: 6 }).map((_, index) => (
              <input
                key={index}
                ref={(el) => (otpInputRefs.current[index] = el)}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={otpValue[index] || ''}
                onChange={(e) => handleOTPChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={handlePaste}
                disabled={isLoading}
                className={cn(
                  "w-12 h-12 text-center text-lg font-semibold border rounded-md transition-colors",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  errors.otp
                    ? "border-red-300 bg-red-50 text-red-900"
                    : "border-gray-300 bg-white text-gray-900",
                  isLoading && "bg-gray-50 cursor-not-allowed"
                )}
              />
            ))}
          </div>
          {errors.otp && (
            <p className="text-xs text-red-600 text-center">{errors.otp.message}</p>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600 text-center">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={!isValid || isLoading}
          className={cn(
            "w-full flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            isValid && !isLoading
              ? "bg-blue-600 text-white hover:bg-blue-700"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          )}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Verifying...
            </>
          ) : (
            'Verify Code'
          )}
        </button>

        {/* Resend OTP */}
        <div className="text-center">
          <p className="text-sm text-gray-600 mb-2">
            Didn't receive the code?
          </p>
          <button
            type="button"
            onClick={handleResendOTP}
            disabled={resendCooldown > 0 || isLoading}
            className={cn(
              "inline-flex items-center text-sm font-medium transition-colors",
              "focus:outline-none focus:underline",
              resendCooldown > 0 || isLoading
                ? "text-gray-400 cursor-not-allowed"
                : "text-blue-600 hover:text-blue-700"
            )}
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            {resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend Code'}
          </button>
        </div>

        {/* Back Button */}
        {onBack && (
          <button
            type="button"
            onClick={onBack}
            disabled={isLoading}
            className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Change Phone Number
          </button>
        )}
      </form>
    </div>
  );
}
