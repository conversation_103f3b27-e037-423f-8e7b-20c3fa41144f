'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Phone, Loader2 } from 'lucide-react';
import { loginSchema, LoginFormData } from '@/lib/validations';
import { useAuthStore } from '@/stores/authStore';
import { useUIStore } from '@/stores/uiStore';
import { CountrySelector } from './CountrySelector';
import { cn } from '@/lib/utils';

interface LoginFormProps {
  onSuccess?: () => void;
}

export function LoginForm({ onSuccess }: LoginFormProps) {
  const { login, isLoading, error } = useAuthStore();
  const { showToast } = useUIStore();
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
    defaultValues: {
      countryCode: '',
      phoneNumber: '',
    },
  });

  const countryCode = watch('countryCode');

  const onSubmit = async (data: LoginFormData) => {
    try {
      await login(data);
      showToast({
        type: 'success',
        message: `OTP sent to ${data.countryCode} ${data.phoneNumber}`,
      });
      onSuccess?.();
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to send OTP. Please try again.',
      });
    }
  };

  const handleCountryChange = (newCountryCode: string) => {
    setValue('countryCode', newCountryCode, { shouldValidate: true });
  };

  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow digits
    const value = e.target.value.replace(/\D/g, '');
    setValue('phoneNumber', value, { shouldValidate: true });
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <Phone className="w-6 h-6 text-blue-600" />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome to Gemini Chat
        </h1>
        <p className="text-gray-600">
          Enter your phone number to get started
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Country Code Selector */}
        <div>
          <label htmlFor="countryCode" className="block text-sm font-medium text-gray-700 mb-2">
            Country
          </label>
          <CountrySelector
            value={countryCode}
            onChange={handleCountryChange}
            error={errors.countryCode?.message}
            disabled={isLoading}
          />
        </div>

        {/* Phone Number Input */}
        <div>
          <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-2">
            Phone Number
          </label>
          <div className="relative">
            <input
              {...register('phoneNumber')}
              type="tel"
              placeholder="Enter your phone number"
              onChange={handlePhoneNumberChange}
              disabled={isLoading}
              className={cn(
                "w-full px-3 py-2 border rounded-md text-sm transition-colors",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "placeholder:text-gray-400",
                errors.phoneNumber
                  ? "border-red-300 bg-red-50 text-red-900"
                  : "border-gray-300 bg-white text-gray-900",
                isLoading && "bg-gray-50 cursor-not-allowed"
              )}
            />
          </div>
          {errors.phoneNumber && (
            <p className="mt-1 text-xs text-red-600">{errors.phoneNumber.message}</p>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={!isValid || isLoading}
          className={cn(
            "w-full flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            isValid && !isLoading
              ? "bg-blue-600 text-white hover:bg-blue-700"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          )}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Sending OTP...
            </>
          ) : (
            'Send OTP'
          )}
        </button>
      </form>

      {/* Info Text */}
      <div className="mt-6 text-center">
        <p className="text-xs text-gray-500">
          By continuing, you agree to our Terms of Service and Privacy Policy.
          We'll send you a 6-digit verification code via SMS.
        </p>
      </div>
    </div>
  );
}
