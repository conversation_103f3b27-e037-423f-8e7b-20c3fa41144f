'use client';

import { useState, useEffect, useRef } from 'react';
import { X, Loader2, MessageCircle } from 'lucide-react';
import { CreateChatroomFormData } from '@/lib/validations';
import { cn } from '@/lib/utils';

interface CreateChatroomModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateChatroomFormData) => Promise<void>;
  isLoading?: boolean;
}

export function CreateChatroomModal({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
}: CreateChatroomModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const [title, setTitle] = useState('');
  const [error, setError] = useState('');
  const [touched, setTouched] = useState(false);

  // Validation logic
  const validateTitle = (value: string): string => {
    const trimmed = value.trim();
    if (!trimmed) {
      return 'Chat title is required';
    }
    if (trimmed.length > 50) {
      return 'Chat title must be at most 50 characters';
    }
    return '';
  };

  // Check if form is valid
  const isFormValid = title.trim().length > 0 && title.trim().length <= 50;

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setTitle(value);

    if (touched) {
      setError(validateTitle(value));
    }
  };

  // Handle input blur
  const handleInputBlur = () => {
    setTouched(true);
    setError(validateTitle(title));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateTitle(title);
    if (validationError) {
      setError(validationError);
      setTouched(true);
      return;
    }

    try {
      await onSubmit({ title: title.trim() });
      handleClose();
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  // Handle modal close
  const handleClose = () => {
    setTitle('');
    setError('');
    setTouched(false);
    onClose();
  };

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen]);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div
          ref={modalRef}
          className="relative w-full max-w-md bg-white rounded-lg shadow-xl transform transition-all"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <MessageCircle className="w-4 h-4 text-blue-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-900">
                New Chat
              </h2>
            </div>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="p-1 text-gray-400 hover:text-gray-600 rounded transition-colors disabled:opacity-50"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6">
            <div className="mb-6">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Chat Title
              </label>
              <input
                ref={inputRef}
                type="text"
                value={title}
                onChange={handleInputChange}
                onBlur={handleInputBlur}
                placeholder="Enter a title for your chat..."
                disabled={isLoading}
                className={cn(
                  "w-full px-3 py-2 border rounded-md text-sm transition-colors",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "placeholder:text-gray-400",
                  error && touched
                    ? "border-red-300 bg-red-50 text-red-900"
                    : "border-gray-300 bg-white text-gray-900",
                  isLoading && "bg-gray-50 cursor-not-allowed"
                )}
              />
              {error && touched && (
                <p className="mt-1 text-xs text-red-600">{error}</p>
              )}
            </div>

            {/* Buttons */}
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!isFormValid || isLoading}
                className={cn(
                  "flex-1 flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                  isFormValid && !isLoading
                    ? "bg-blue-600 text-white hover:bg-blue-700"
                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                )}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Chat'
                )}
              </button>
            </div>
          </form>

          {/* Tips */}
          <div className="px-6 pb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <p className="text-xs text-blue-700">
                💡 <strong>Tip:</strong> Give your chat a descriptive title to easily find it later.
                You can always start chatting with Gemini right away!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
