'use client';

import { useState, useEffect } from 'react';
import { <PERSON>u, Sun, Moon, LogOut, User } from 'lucide-react';
import { useChatStore } from '@/stores/chatStore';
import { useAuthStore } from '@/stores/authStore';
import { useUIStore } from '@/stores/uiStore';
import { ChatroomList } from './ChatroomList';
import { CreateChatroomModal } from './CreateChatroomModal';
import { ChatInterface } from '../chat/ChatInterface';
import { CreateChatroomFormData } from '@/lib/validations';
import { debounce, cn } from '@/lib/utils';

export function Dashboard() {
  const {
    chatrooms,
    currentChatroom,
    searchQuery,
    isLoading,
    createChatroom,
    deleteChatroom,
    selectChatroom,
    setSearchQuery,
  } = useChatStore();

  const { user, logout } = useAuthStore();
  const { theme, sidebarOpen, toggleTheme, toggleSidebar, showToast } = useUIStore();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Debounced search function
  const debouncedSearch = debounce((query: string) => {
    setSearchQuery(query);
  }, 300);

  const handleSearchChange = (query: string) => {
    debouncedSearch(query);
  };

  const handleCreateChatroom = async (data: CreateChatroomFormData) => {
    try {
      await createChatroom(data);
      showToast({
        type: 'success',
        message: 'Chat created successfully!',
      });
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to create chat',
      });
      throw error;
    }
  };

  const handleDeleteChatroom = async (chatroomId: string) => {
    try {
      await deleteChatroom(chatroomId);
    } catch (error) {
      throw error;
    }
  };

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to log out?')) {
      logout();
      showToast({
        type: 'info',
        message: 'Logged out successfully',
      });
    }
  };

  return (
    <div className={cn(
      "flex h-screen bg-gray-50",
      theme === 'dark' && "dark"
    )}>
      {/* Sidebar */}
      <div className={cn(
        "flex-shrink-0 transition-all duration-300 ease-in-out",
        sidebarOpen ? "w-80" : "w-0",
        isMobile && sidebarOpen && "absolute inset-y-0 left-0 z-40 w-80"
      )}>
        <div className="h-full overflow-hidden">
          <ChatroomList
            chatrooms={chatrooms}
            currentChatroomId={currentChatroom?.id}
            onSelectChatroom={selectChatroom}
            onDeleteChatroom={handleDeleteChatroom}
            onCreateChatroom={() => setIsCreateModalOpen(true)}
            searchQuery={searchQuery}
            onSearchChange={handleSearchChange}
            isLoading={isLoading}
          />
        </div>
      </div>

      {/* Mobile overlay */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50"
          onClick={toggleSidebar}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={toggleSidebar}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
              >
                <Menu className="w-5 h-5" />
              </button>
              
              {currentChatroom ? (
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">
                    {currentChatroom.title}
                  </h1>
                  <p className="text-sm text-gray-500">
                    Chat with Gemini AI
                  </p>
                </div>
              ) : (
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">
                    Gemini Chat
                  </h1>
                  <p className="text-sm text-gray-500">
                    Select a chat to start messaging
                  </p>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
              >
                {theme === 'light' ? (
                  <Moon className="w-5 h-5" />
                ) : (
                  <Sun className="w-5 h-5" />
                )}
              </button>

              {/* User Menu */}
              <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-md">
                <User className="w-4 h-4 text-gray-600" />
                <span className="text-sm text-gray-700">
                  {user?.countryCode} {user?.phoneNumber}
                </span>
                <button
                  onClick={handleLogout}
                  className="p-1 text-gray-600 hover:text-red-600 rounded transition-colors"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Chat Area */}
        <div className="flex-1 overflow-hidden">
          {currentChatroom ? (
            <ChatInterface chatroom={currentChatroom} />
          ) : (
            <div className="flex items-center justify-center h-full bg-white">
              <div className="text-center max-w-md mx-auto p-8">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Menu className="w-8 h-8 text-blue-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Welcome to Gemini Chat
                </h2>
                <p className="text-gray-600 mb-6">
                  Select an existing chat from the sidebar or create a new one to start 
                  your conversation with Gemini AI.
                </p>
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Start New Chat
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Chatroom Modal */}
      <CreateChatroomModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateChatroom}
        isLoading={isLoading}
      />
    </div>
  );
}
